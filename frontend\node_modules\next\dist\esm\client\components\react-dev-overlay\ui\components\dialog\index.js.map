{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/index.ts"], "sourcesContent": ["export { Dialog } from './dialog'\nexport { DialogBody } from './dialog-body'\nexport { DialogContent } from './dialog-content'\nexport { DialogHeader } from './dialog-header'\nexport { styles } from './styles'\n"], "names": ["Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "styles"], "mappings": "AAAA,SAASA,MAAM,QAAQ,WAAU;AACjC,SAASC,UAAU,QAAQ,gBAAe;AAC1C,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,MAAM,QAAQ,WAAU"}