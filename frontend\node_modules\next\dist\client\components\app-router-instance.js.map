{"version": 3, "sources": ["../../../src/client/components/app-router-instance.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          options?.kind === PrefetchKind.FULL\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n"], "names": ["createMutableActionQueue", "dispatchNavigateAction", "dispatchTraverseAction", "getCurrentAppRouterState", "publicAppRouterInstance", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "ACTION_REFRESH", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "isThenable", "then", "err", "reject", "dispatchAction", "resolvers", "ACTION_RESTORE", "deferred<PERSON><PERSON><PERSON>", "Promise", "startTransition", "newAction", "last", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "globalActionQueue", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "result", "reducer", "onRouterTransitionStart", "Error", "getAppRouterActionQueue", "getProfilingHookForOnNavigationStart", "href", "navigateType", "shouldScroll", "linkInstanceRef", "url", "URL", "addBasePath", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "__pendingUrl", "setLinkForCurrentNavigation", "dispatchAppRouterAction", "isExternalUrl", "isExternalURL", "locationSearch", "search", "allowAliasing", "tree", "back", "history", "forward", "prefetch", "__NEXT_CLIENT_SEGMENT_CACHE", "options", "prefetchWithSegmentCache", "nextUrl", "kind", "PrefetchKind", "FULL", "createPrefetchURL", "prefetchReducer", "ACTION_PREFETCH", "replace", "scroll", "push", "refresh", "hmrRefresh", "NODE_ENV", "ACTION_HMR_REFRESH", "router"], "mappings": ";;;;;;;;;;;;;;;;;;IAyMgBA,wBAAwB;eAAxBA;;IA0DAC,sBAAsB;eAAtBA;;IA+BAC,sBAAsB;eAAtBA;;IAnDAC,wBAAwB;eAAxBA;;IAuEHC,uBAAuB;eAAvBA;;;oCA1SN;+BACiB;uBACQ;4BACL;8BAC0B;gCACb;6BACZ;2BACqB;iCACjB;uBAM+B;AA4B/D,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAMC,kCAAc;oBACpBC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAT;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMY,YAAYb,YAAYc,KAAK;IAEnCd,YAAYE,OAAO,GAAGG;IAEtB,MAAMU,UAAUV,OAAOU,OAAO;IAC9B,MAAMC,eAAehB,YAAYK,MAAM,CAACQ,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIb,OAAOc,SAAS,EAAE;YACpB;QACF;QAEAnB,YAAYc,KAAK,GAAGI;QAEpBnB,oBAAoBC,aAAaC;QACjCI,OAAOe,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,IAAIG,IAAAA,sBAAU,EAACL,eAAe;QAC5BA,aAAaM,IAAI,CAACL,cAAc,CAACM;YAC/BxB,oBAAoBC,aAAaC;YACjCI,OAAOmB,MAAM,CAACD;QAChB;IACF,OAAO;QACLN,aAAaD;IACf;AACF;AAEA,SAASS,eACPzB,WAAiC,EACjCe,OAAuB,EACvBd,QAA8B;IAE9B,IAAIyB,YAGA;QAAEN,SAASnB;QAAUuB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIT,QAAQP,IAAI,KAAKmB,kCAAc,EAAE;QACnC,6DAA6D;QAC7D,MAAMC,kBAAkB,IAAIC,QAAwB,CAACT,SAASI;YAC5DE,YAAY;gBAAEN;gBAASI;YAAO;QAChC;QAEAM,IAAAA,sBAAe,EAAC;YACd,oGAAoG;YACpG,iEAAiE;YACjE7B,SAAS2B;QACX;IACF;IAEA,MAAMG,YAA6B;QACjChB;QACAZ,MAAM;QACNiB,SAASM,UAAUN,OAAO;QAC1BI,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAIxB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAYgC,IAAI,GAAGD;QAEnB3B,UAAU;YACRJ;YACAK,QAAQ0B;YACR9B;QACF;IACF,OAAO,IACLc,QAAQP,IAAI,KAAKyB,mCAAe,IAChClB,QAAQP,IAAI,KAAKmB,kCAAc,EAC/B;QACA,+EAA+E;QAC/E,oHAAoH;QACpH3B,YAAYE,OAAO,CAACiB,SAAS,GAAG;QAEhC,4EAA4E;QAC5E,sIAAsI;QACtIY,UAAU5B,IAAI,GAAGH,YAAYE,OAAO,CAACC,IAAI;QAEzC,2GAA2G;QAC3G,IAAIH,YAAYE,OAAO,CAACa,OAAO,CAACP,IAAI,KAAK0B,wCAAoB,EAAE;YAC7DlC,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQ0B;YACR9B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAYgC,IAAI,KAAK,MAAM;YAC7BhC,YAAYgC,IAAI,CAAC7B,IAAI,GAAG4B;QAC1B;QACA/B,YAAYgC,IAAI,GAAGD;IACrB;AACF;AAEA,IAAII,oBAAiD;AAE9C,SAASzC,yBACd0C,YAA4B,EAC5BC,oBAAuD;IAEvD,MAAMrC,cAAoC;QACxCc,OAAOsB;QACP7B,UAAU,CAACQ,SAAyBd,WAClCwB,eAAezB,aAAae,SAASd;QACvCI,QAAQ,OAAOS,OAAuBT;YACpC,MAAMiC,SAASC,IAAAA,sBAAO,EAACzB,OAAOT;YAC9B,OAAOiC;QACT;QACApC,SAAS;QACT8B,MAAM;QACNQ,yBACEH,yBAAyB,QACzB,OAAOA,qBAAqBG,uBAAuB,KAAK,aAEpDH,qBAAqBG,uBAAuB,GAC5C;IACR;IAEA,IAAI,OAAO7B,WAAW,aAAa;QACjC,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C,IAAIwB,sBAAsB,MAAM;YAC9B,MAAM,qBAGL,CAHK,IAAIM,MACR,sEACE,cAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACAN,oBAAoBnC;IACtB;IAEA,OAAOA;AACT;AAEO,SAASH;IACd,OAAOsC,sBAAsB,OAAOA,kBAAkBrB,KAAK,GAAG;AAChE;AAEA,SAAS4B;IACP,IAAIP,sBAAsB,MAAM;QAC9B,MAAM,qBAEL,CAFK,IAAIM,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,OAAON;AACT;AAEA,SAASQ;IACP,IAAIR,sBAAsB,MAAM;QAC9B,OAAOA,kBAAkBK,uBAAuB;IAClD;IACA,OAAO;AACT;AAEO,SAAS7C,uBACdiD,IAAY,EACZC,YAA4C,EAC5CC,YAAqB,EACrBC,eAAoC;IAEpC,yEAAyE;IACzE,oEAAoE;IACpE,MAAMC,MAAM,IAAIC,IAAIC,IAAAA,wBAAW,EAACN,OAAOhC,SAASgC,IAAI;IACpD,IAAIO,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QAC5C1C,OAAOR,IAAI,CAACmD,YAAY,GAAGN;IAC7B;IAEAO,IAAAA,kCAA2B,EAACR;IAE5B,MAAMP,0BAA0BG;IAChC,IAAIH,4BAA4B,MAAM;QACpCA,wBAAwBI,MAAMC;IAChC;IAEAW,IAAAA,uCAAuB,EAAC;QACtBhD,MAAMyB,mCAAe;QACrBe;QACAS,eAAeC,IAAAA,wBAAa,EAACV;QAC7BW,gBAAgB/C,SAASgD,MAAM;QAC/Bd;QACAD;QACAgB,eAAe;IACjB;AACF;AAEO,SAASjE,uBACdgD,IAAY,EACZkB,IAAmC;IAEnC,MAAMtB,0BAA0BG;IAChC,IAAIH,4BAA4B,MAAM;QACpCA,wBAAwBI,MAAM;IAChC;IACAY,IAAAA,uCAAuB,EAAC;QACtBhD,MAAMmB,kCAAc;QACpBqB,KAAK,IAAIC,IAAIL;QACbkB;IACF;AACF;AAOO,MAAMhE,0BAA6C;IACxDiE,MAAM,IAAMpD,OAAOqD,OAAO,CAACD,IAAI;IAC/BE,SAAS,IAAMtD,OAAOqD,OAAO,CAACC,OAAO;IACrCC,UAAUf,QAAQC,GAAG,CAACe,2BAA2B,GAE7C,oEAAoE;IACpE,iDAAiD;IACjD,CAACvB,MAAcwB;QACb,MAAMpE,cAAc0C;QACpB2B,IAAAA,sBAAwB,EACtBzB,MACA5C,YAAYc,KAAK,CAACwD,OAAO,EACzBtE,YAAYc,KAAK,CAACgD,IAAI,EACtBM,CAAAA,2BAAAA,QAASG,IAAI,MAAKC,gCAAY,CAACC,IAAI;IAEvC,IACA,CAAC7B,MAAcwB;QACb,uCAAuC;QACvC,MAAMpE,cAAc0C;QACpB,MAAMM,MAAM0B,IAAAA,4BAAiB,EAAC9B;QAC9B,IAAII,QAAQ,MAAM;gBAURoB;YATR,4DAA4D;YAC5D,+DAA+D;YAC/D,iEAAiE;YACjE,gDAAgD;YAChD,oDAAoD;YACpD,kBAAkB;YAClBO,IAAAA,gCAAe,EAAC3E,YAAYc,KAAK,EAAE;gBACjCN,MAAMoE,mCAAe;gBACrB5B;gBACAuB,MAAMH,CAAAA,gBAAAA,2BAAAA,QAASG,IAAI,YAAbH,gBAAiBI,gCAAY,CAACC,IAAI;YAC1C;QACF;IACF;IACJI,SAAS,CAACjC,MAAcwB;QACtBtC,IAAAA,sBAAe,EAAC;gBAC0BsC;YAAxCzE,uBAAuBiD,MAAM,WAAWwB,CAAAA,kBAAAA,2BAAAA,QAASU,MAAM,YAAfV,kBAAmB,MAAM;QACnE;IACF;IACAW,MAAM,CAACnC,MAAcwB;QACnBtC,IAAAA,sBAAe,EAAC;gBACuBsC;YAArCzE,uBAAuBiD,MAAM,QAAQwB,CAAAA,kBAAAA,2BAAAA,QAASU,MAAM,YAAfV,kBAAmB,MAAM;QAChE;IACF;IACAY,SAAS;QACPlD,IAAAA,sBAAe,EAAC;YACd0B,IAAAA,uCAAuB,EAAC;gBACtBhD,MAAMC,kCAAc;gBACpBC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;YAChC;QACF;IACF;IACAuE,YAAY;QACV,IAAI9B,QAAQC,GAAG,CAAC8B,QAAQ,KAAK,eAAe;YAC1C,MAAM,qBAEL,CAFK,IAAIzC,MACR,iFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO;YACLX,IAAAA,sBAAe,EAAC;gBACd0B,IAAAA,uCAAuB,EAAC;oBACtBhD,MAAM2E,sCAAkB;oBACxBzE,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC;YACF;QACF;IACF;AACF;AAEA,gEAAgE;AAChE,IAAI,OAAOC,WAAW,eAAeA,OAAOR,IAAI,EAAE;IAChDQ,OAAOR,IAAI,CAACiF,MAAM,GAAGtF;AACvB"}