{"version": 3, "sources": ["../../../../src/client/dev/dev-build-indicator/initialize-for-app-router.ts"], "sourcesContent": ["import { devBuildIndicator } from './internal/dev-build-indicator'\n\n/** Integrates the generic dev build indicator with the App Router. */\nexport const initializeDevBuildIndicatorForAppRouter = () => {\n  if (!process.env.__NEXT_DEV_INDICATOR) {\n    return\n  }\n\n  devBuildIndicator.initialize()\n}\n"], "names": ["initializeDevBuildIndicatorForAppRouter", "process", "env", "__NEXT_DEV_INDICATOR", "devBuildIndicator", "initialize"], "mappings": ";;;;+BAGaA;;;eAAAA;;;mCAHqB;AAG3B,MAAMA,0CAA0C;IACrD,IAAI,CAACC,QAAQC,GAAG,CAACC,oBAAoB,EAAE;QACrC;IACF;IAEAC,oCAAiB,CAACC,UAAU;AAC9B"}