{"version": 3, "sources": ["../../../src/build/output/format.ts"], "sourcesContent": ["import type { CacheControl } from '../../server/lib/cache-control'\n\nconst timeUnits = [\n  { label: 'y', seconds: 31536000 },\n  { label: 'w', seconds: 604800 },\n  { label: 'd', seconds: 86400 },\n  { label: 'h', seconds: 3600 },\n  { label: 'm', seconds: 60 },\n  { label: 's', seconds: 1 },\n]\n\nfunction humanReadableTimeRounded(seconds: number): string {\n  // Find the largest fitting unit.\n  let candidateIndex = timeUnits.length - 1\n  for (let i = 0; i < timeUnits.length; i++) {\n    if (seconds >= timeUnits[i].seconds) {\n      candidateIndex = i\n      break\n    }\n  }\n\n  const candidate = timeUnits[candidateIndex]\n  const value = seconds / candidate.seconds\n  const isExact = Number.isInteger(value)\n\n  // For days and weeks only, check if using the next smaller unit yields an\n  // exact result.\n  if (!isExact && (candidate.label === 'd' || candidate.label === 'w')) {\n    const nextUnit = timeUnits[candidateIndex + 1]\n    const nextValue = seconds / nextUnit.seconds\n\n    if (Number.isInteger(nextValue)) {\n      return `${nextValue}${nextUnit.label}`\n    }\n  }\n\n  if (isExact) {\n    return `${value}${candidate.label}`\n  }\n\n  return `≈${Math.round(value)}${candidate.label}`\n}\n\nexport function formatRevalidate(cacheControl: CacheControl): string {\n  const { revalidate } = cacheControl\n\n  return revalidate ? humanReadableTimeRounded(revalidate) : ''\n}\n\nexport function formatExpire(cacheControl: CacheControl): string {\n  const { expire } = cacheControl\n\n  return expire ? humanReadableTimeRounded(expire) : ''\n}\n"], "names": ["timeUnits", "label", "seconds", "humanReadableTimeRounded", "candidateIndex", "length", "i", "candidate", "value", "isExact", "Number", "isInteger", "nextUnit", "nextValue", "Math", "round", "formatRevalidate", "cacheControl", "revalidate", "formatExpire", "expire"], "mappings": "AAEA,MAAMA,YAAY;IAChB;QAAEC,OAAO;QAAKC,SAAS;IAAS;IAChC;QAAED,OAAO;QAAKC,SAAS;IAAO;IAC9B;QAAED,OAAO;QAAKC,SAAS;IAAM;IAC7B;QAAED,OAAO;QAAKC,SAAS;IAAK;IAC5B;QAAED,OAAO;QAAKC,SAAS;IAAG;IAC1B;QAAED,OAAO;QAAKC,SAAS;IAAE;CAC1B;AAED,SAASC,yBAAyBD,OAAe;IAC/C,iCAAiC;IACjC,IAAIE,iBAAiBJ,UAAUK,MAAM,GAAG;IACxC,IAAK,IAAIC,IAAI,GAAGA,IAAIN,UAAUK,MAAM,EAAEC,IAAK;QACzC,IAAIJ,WAAWF,SAAS,CAACM,EAAE,CAACJ,OAAO,EAAE;YACnCE,iBAAiBE;YACjB;QACF;IACF;IAEA,MAAMC,YAAYP,SAAS,CAACI,eAAe;IAC3C,MAAMI,QAAQN,UAAUK,UAAUL,OAAO;IACzC,MAAMO,UAAUC,OAAOC,SAAS,CAACH;IAEjC,0EAA0E;IAC1E,gBAAgB;IAChB,IAAI,CAACC,WAAYF,CAAAA,UAAUN,KAAK,KAAK,OAAOM,UAAUN,KAAK,KAAK,GAAE,GAAI;QACpE,MAAMW,WAAWZ,SAAS,CAACI,iBAAiB,EAAE;QAC9C,MAAMS,YAAYX,UAAUU,SAASV,OAAO;QAE5C,IAAIQ,OAAOC,SAAS,CAACE,YAAY;YAC/B,OAAO,GAAGA,YAAYD,SAASX,KAAK,EAAE;QACxC;IACF;IAEA,IAAIQ,SAAS;QACX,OAAO,GAAGD,QAAQD,UAAUN,KAAK,EAAE;IACrC;IAEA,OAAO,CAAC,CAAC,EAAEa,KAAKC,KAAK,CAACP,SAASD,UAAUN,KAAK,EAAE;AAClD;AAEA,OAAO,SAASe,iBAAiBC,YAA0B;IACzD,MAAM,EAAEC,UAAU,EAAE,GAAGD;IAEvB,OAAOC,aAAaf,yBAAyBe,cAAc;AAC7D;AAEA,OAAO,SAASC,aAAaF,YAA0B;IACrD,MAAM,EAAEG,MAAM,EAAE,GAAGH;IAEnB,OAAOG,SAASjB,yBAAyBiB,UAAU;AACrD"}