{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/get-error-by-type.ts"], "sourcesContent": ["import { ACTION_UNHANDLED_ERROR, ACTION_UNHANDLED_REJECTION } from '../shared'\nimport type { SupportedErrorEvent } from '../ui/container/runtime-error/render-error'\nimport { getOriginalStackFrames } from './stack-frame'\nimport type { OriginalStackFrame } from './stack-frame'\nimport type { ComponentStackFrame } from './parse-component-stack'\nimport { getErrorSource } from '../../../../shared/lib/error-source'\nimport React from 'react'\n\nexport type ReadyRuntimeError = {\n  id: number\n  runtime: true\n  error: Error & { environmentName?: string }\n  frames: OriginalStackFrame[] | (() => Promise<OriginalStackFrame[]>)\n  componentStackFrames?: ComponentStackFrame[]\n}\n\nexport const useFrames = (error: ReadyRuntimeError): OriginalStackFrame[] => {\n  if ('use' in React) {\n    const frames = error.frames\n\n    if (typeof frames !== 'function') {\n      throw new Error(\n        'Invariant: frames must be a function when the React version has React.use. This is a bug in Next.js.'\n      )\n    }\n\n    return React.use((frames as () => Promise<OriginalStackFrame[]>)())\n  } else {\n    if (!Array.isArray(error.frames)) {\n      throw new Error(\n        'Invariant: frames must be an array when the React version does not have React.use. This is a bug in Next.js.'\n      )\n    }\n\n    return error.frames\n  }\n}\n\nexport async function getErrorByType(\n  ev: SupportedErrorEvent,\n  isAppDir: boolean\n): Promise<ReadyRuntimeError> {\n  const { id, event } = ev\n  switch (event.type) {\n    case ACTION_UNHANDLED_ERROR:\n    case ACTION_UNHANDLED_REJECTION: {\n      const baseError = {\n        id,\n        runtime: true,\n        error: event.reason,\n      } as const\n\n      if ('use' in React) {\n        const readyRuntimeError: ReadyRuntimeError = {\n          ...baseError,\n          // createMemoizedPromise dedups calls to getOriginalStackFrames\n          frames: createMemoizedPromise(async () => {\n            return await getOriginalStackFrames(\n              event.frames,\n              getErrorSource(event.reason),\n              isAppDir\n            )\n          }),\n        }\n        if (event.type === ACTION_UNHANDLED_ERROR) {\n          readyRuntimeError.componentStackFrames = event.componentStackFrames\n        }\n        return readyRuntimeError\n      } else {\n        const readyRuntimeError: ReadyRuntimeError = {\n          ...baseError,\n          // createMemoizedPromise dedups calls to getOriginalStackFrames\n          frames: await getOriginalStackFrames(\n            event.frames,\n            getErrorSource(event.reason),\n            isAppDir\n          ),\n        }\n        if (event.type === ACTION_UNHANDLED_ERROR) {\n          readyRuntimeError.componentStackFrames = event.componentStackFrames\n        }\n        return readyRuntimeError\n      }\n    }\n    default: {\n      break\n    }\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const _: never = event\n  throw new Error('type system invariant violation')\n}\n\nfunction createMemoizedPromise<T>(\n  promiseFactory: () => Promise<T>\n): () => Promise<T> {\n  const cachedPromise = promiseFactory()\n  return function (): Promise<T> {\n    return cachedPromise\n  }\n}\n"], "names": ["ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "getOriginalStackFrames", "getErrorSource", "React", "useFrames", "error", "frames", "Error", "use", "Array", "isArray", "getErrorByType", "ev", "isAppDir", "id", "event", "type", "baseError", "runtime", "reason", "readyRuntimeError", "createMemoizedPromise", "componentStackFrames", "_", "promiseFactory", "cachedPromise"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,0BAA0B,QAAQ,YAAW;AAE9E,SAASC,sBAAsB,QAAQ,gBAAe;AAGtD,SAASC,cAAc,QAAQ,sCAAqC;AACpE,OAAOC,WAAW,QAAO;AAUzB,OAAO,MAAMC,YAAY,CAACC;IACxB,IAAI,SAASF,OAAO;QAClB,MAAMG,SAASD,MAAMC,MAAM;QAE3B,IAAI,OAAOA,WAAW,YAAY;YAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,yGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOJ,MAAMK,GAAG,CAAC,AAACF;IACpB,OAAO;QACL,IAAI,CAACG,MAAMC,OAAO,CAACL,MAAMC,MAAM,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,iHADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOF,MAAMC,MAAM;IACrB;AACF,EAAC;AAED,OAAO,eAAeK,eACpBC,EAAuB,EACvBC,QAAiB;IAEjB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGH;IACtB,OAAQG,MAAMC,IAAI;QAChB,KAAKjB;QACL,KAAKC;YAA4B;gBAC/B,MAAMiB,YAAY;oBAChBH;oBACAI,SAAS;oBACTb,OAAOU,MAAMI,MAAM;gBACrB;gBAEA,IAAI,SAAShB,OAAO;oBAClB,MAAMiB,oBAAuC;wBAC3C,GAAGH,SAAS;wBACZ,+DAA+D;wBAC/DX,QAAQe,sBAAsB;4BAC5B,OAAO,MAAMpB,uBACXc,MAAMT,MAAM,EACZJ,eAAea,MAAMI,MAAM,GAC3BN;wBAEJ;oBACF;oBACA,IAAIE,MAAMC,IAAI,KAAKjB,wBAAwB;wBACzCqB,kBAAkBE,oBAAoB,GAAGP,MAAMO,oBAAoB;oBACrE;oBACA,OAAOF;gBACT,OAAO;oBACL,MAAMA,oBAAuC;wBAC3C,GAAGH,SAAS;wBACZ,+DAA+D;wBAC/DX,QAAQ,MAAML,uBACZc,MAAMT,MAAM,EACZJ,eAAea,MAAMI,MAAM,GAC3BN;oBAEJ;oBACA,IAAIE,MAAMC,IAAI,KAAKjB,wBAAwB;wBACzCqB,kBAAkBE,oBAAoB,GAAGP,MAAMO,oBAAoB;oBACrE;oBACA,OAAOF;gBACT;YACF;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMG,IAAWR;IACjB,MAAM,qBAA4C,CAA5C,IAAIR,MAAM,oCAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2C;AACnD;AAEA,SAASc,sBACPG,cAAgC;IAEhC,MAAMC,gBAAgBD;IACtB,OAAO;QACL,OAAOC;IACT;AACF"}