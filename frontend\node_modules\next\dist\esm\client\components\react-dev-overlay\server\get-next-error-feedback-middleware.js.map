{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/get-next-error-feedback-middleware.ts"], "sourcesContent": ["import { eventErrorFeedback } from '../../../../telemetry/events/error-feedback'\nimport { middlewareResponse } from './middleware-response'\nimport type { ServerResponse, IncomingMessage } from 'http'\nimport type { Telemetry } from '../../../../telemetry/storage'\n\n// Handles HTTP requests to /__nextjs_error_feedback endpoint for collecting user feedback on error messages\nexport function getNextErrorFeedbackMiddleware(telemetry: Telemetry) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    if (pathname !== '/__nextjs_error_feedback') {\n      return next()\n    }\n\n    try {\n      const errorCode = searchParams.get('errorCode')\n      const wasHelpful = searchParams.get('wasHelpful')\n\n      if (!errorCode || !wasHelpful) {\n        return middlewareResponse.badRequest(res)\n      }\n\n      await telemetry.record(\n        eventErrorFeedback({\n          errorCode,\n          wasHelpful: wasHelpful === 'true',\n        })\n      )\n\n      return middlewareResponse.noContent(res)\n    } catch (error) {\n      return middlewareResponse.internalServerError(res)\n    }\n  }\n}\n"], "names": ["eventErrorFeedback", "middlewareResponse", "getNextErrorFeedbackMiddleware", "telemetry", "req", "res", "next", "pathname", "searchParams", "URL", "url", "errorCode", "get", "wasHelpful", "badRequest", "record", "noContent", "error", "internalServerError"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,8CAA6C;AAChF,SAASC,kBAAkB,QAAQ,wBAAuB;AAI1D,4GAA4G;AAC5G,OAAO,SAASC,+BAA+BC,SAAoB;IACjE,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIM,GAAG;QAE7D,IAAIH,aAAa,4BAA4B;YAC3C,OAAOD;QACT;QAEA,IAAI;YACF,MAAMK,YAAYH,aAAaI,GAAG,CAAC;YACnC,MAAMC,aAAaL,aAAaI,GAAG,CAAC;YAEpC,IAAI,CAACD,aAAa,CAACE,YAAY;gBAC7B,OAAOZ,mBAAmBa,UAAU,CAACT;YACvC;YAEA,MAAMF,UAAUY,MAAM,CACpBf,mBAAmB;gBACjBW;gBACAE,YAAYA,eAAe;YAC7B;YAGF,OAAOZ,mBAAmBe,SAAS,CAACX;QACtC,EAAE,OAAOY,OAAO;YACd,OAAOhB,mBAAmBiB,mBAAmB,CAACb;QAChD;IACF;AACF"}