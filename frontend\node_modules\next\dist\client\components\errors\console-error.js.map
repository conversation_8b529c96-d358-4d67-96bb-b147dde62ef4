{"version": 3, "sources": ["../../../../src/client/components/errors/console-error.ts"], "sourcesContent": ["// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\nconst digestSym = Symbol.for('next.console.error.digest')\nconst consoleTypeSym = Symbol.for('next.console.error.type')\n\n// Represent non Error shape unhandled promise rejections or console.error errors.\n// Those errors will be captured and displayed in Error Overlay.\nexport type ConsoleError = Error & {\n  [digestSym]: 'NEXT_CONSOLE_ERROR'\n  [consoleTypeSym]: 'string' | 'error'\n  environmentName: string\n}\n\nexport function createConsoleError(\n  message: string | Error,\n  environmentName?: string | null\n): ConsoleError {\n  const error = (\n    typeof message === 'string' ? new Error(message) : message\n  ) as ConsoleError\n  error[digestSym] = 'NEXT_CONSOLE_ERROR'\n  error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error'\n\n  if (environmentName && !error.environmentName) {\n    error.environmentName = environmentName\n  }\n\n  return error\n}\n\nexport const isConsoleError = (error: any): error is ConsoleError => {\n  return error && error[digestSym] === 'NEXT_CONSOLE_ERROR'\n}\n\nexport const getConsoleErrorType = (error: ConsoleError) => {\n  return error[consoleTypeSym]\n}\n"], "names": ["createConsoleError", "getConsoleErrorType", "isConsoleError", "digestSym", "Symbol", "for", "consoleTypeSym", "message", "environmentName", "error", "Error"], "mappings": "AAAA,yJAAyJ;;;;;;;;;;;;;;;;;IAYzIA,kBAAkB;eAAlBA;;IAqBHC,mBAAmB;eAAnBA;;IAJAC,cAAc;eAAdA;;;AA5Bb,MAAMC,YAAYC,OAAOC,GAAG,CAAC;AAC7B,MAAMC,iBAAiBF,OAAOC,GAAG,CAAC;AAU3B,SAASL,mBACdO,OAAuB,EACvBC,eAA+B;IAE/B,MAAMC,QACJ,OAAOF,YAAY,WAAW,qBAAkB,CAAlB,IAAIG,MAAMH,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB,KAAIA;IAErDE,KAAK,CAACN,UAAU,GAAG;IACnBM,KAAK,CAACH,eAAe,GAAG,OAAOC,YAAY,WAAW,WAAW;IAEjE,IAAIC,mBAAmB,CAACC,MAAMD,eAAe,EAAE;QAC7CC,MAAMD,eAAe,GAAGA;IAC1B;IAEA,OAAOC;AACT;AAEO,MAAMP,iBAAiB,CAACO;IAC7B,OAAOA,SAASA,KAAK,CAACN,UAAU,KAAK;AACvC;AAEO,MAAMF,sBAAsB,CAACQ;IAClC,OAAOA,KAAK,CAACH,eAAe;AAC9B"}