{"version": 3, "sources": ["../../src/lib/get-network-host.ts"], "sourcesContent": ["import os from 'os'\n\nfunction getNetworkHosts(family: 'IPv4' | 'IPv6'): string[] {\n  const interfaces = os.networkInterfaces()\n  const hosts: string[] = []\n\n  Object.keys(interfaces).forEach((key) => {\n    interfaces[key]\n      ?.filter((networkInterface) => {\n        switch (networkInterface.family) {\n          case 'IPv6':\n            return (\n              family === 'IPv6' &&\n              networkInterface.scopeid === 0 &&\n              networkInterface.address !== '::1'\n            )\n          case 'IPv4':\n            return family === 'IPv4' && networkInterface.address !== '127.0.0.1'\n          default:\n            return false\n        }\n      })\n      .forEach((networkInterface) => {\n        if (networkInterface.address) {\n          hosts.push(networkInterface.address)\n        }\n      })\n  })\n\n  return hosts\n}\n\nexport function getNetworkHost(family: 'IPv4' | 'IPv6'): string | null {\n  const hosts = getNetworkHosts(family)\n  return hosts[0] ?? null\n}\n"], "names": ["getNetworkHost", "getNetworkHosts", "family", "interfaces", "os", "networkInterfaces", "hosts", "Object", "keys", "for<PERSON>ach", "key", "filter", "networkInterface", "scopeid", "address", "push"], "mappings": ";;;;+BAgCgBA;;;eAAAA;;;2DAhCD;;;;;;AAEf,SAASC,gBAAgBC,MAAuB;IAC9C,MAAMC,aAAaC,WAAE,CAACC,iBAAiB;IACvC,MAAMC,QAAkB,EAAE;IAE1BC,OAAOC,IAAI,CAACL,YAAYM,OAAO,CAAC,CAACC;YAC/BP;SAAAA,kBAAAA,UAAU,CAACO,IAAI,qBAAfP,gBACIQ,MAAM,CAAC,CAACC;YACR,OAAQA,iBAAiBV,MAAM;gBAC7B,KAAK;oBACH,OACEA,WAAW,UACXU,iBAAiBC,OAAO,KAAK,KAC7BD,iBAAiBE,OAAO,KAAK;gBAEjC,KAAK;oBACH,OAAOZ,WAAW,UAAUU,iBAAiBE,OAAO,KAAK;gBAC3D;oBACE,OAAO;YACX;QACF,GACCL,OAAO,CAAC,CAACG;YACR,IAAIA,iBAAiBE,OAAO,EAAE;gBAC5BR,MAAMS,IAAI,CAACH,iBAAiBE,OAAO;YACrC;QACF;IACJ;IAEA,OAAOR;AACT;AAEO,SAASN,eAAeE,MAAuB;IACpD,MAAMI,QAAQL,gBAAgBC;IAC9B,OAAOI,KAAK,CAAC,EAAE,IAAI;AACrB"}