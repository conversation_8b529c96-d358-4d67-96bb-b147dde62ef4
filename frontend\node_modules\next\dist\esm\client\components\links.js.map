{"version": 3, "sources": ["../../../src/client/components/links.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from './app-router-instance'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  reschedulePrefetchTask,\n} from './segment-cache'\nimport { startTransition } from 'react'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement\n\ntype Element = LinkElement | HTMLFormElement\n\n// Properties that are shared between Link and Form instances. We use the same\n// shape for both to prevent a polymorphic de-opt in the VM.\ntype LinkOrFormInstanceShared = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\nexport type FormInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: null\n}\n\ntype PrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype NonPrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: null\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype PrefetchableInstance = PrefetchableLinkInstance | FormInstance\n\nexport type LinkInstance =\n  | PrefetchableLinkInstance\n  | NonPrefetchableLinkInstance\n\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation: LinkInstance | null = null\n\n// Status object indicating link is pending\nexport const PENDING_LINK_STATUS = { pending: true }\n\n// Status object indicating link is idle\nexport const IDLE_LINK_STATUS = { pending: false }\n\n// Updates the loading state when navigating between links\n// - Resets the previous link's loading state\n// - Sets the new link's loading state\n// - Updates tracking of current navigation\nexport function setLinkForCurrentNavigation(link: LinkInstance | null) {\n  startTransition(() => {\n    linkForMostRecentNavigation?.setOptimisticLinkStatus(IDLE_LINK_STATUS)\n    link?.setOptimisticLinkStatus(PENDING_LINK_STATUS)\n    linkForMostRecentNavigation = link\n  })\n}\n\n// Unmounts the current link instance from navigation tracking\nexport function unmountLinkForCurrentNavigation(link: LinkInstance) {\n  if (linkForMostRecentNavigation === link) {\n    linkForMostRecentNavigation = null\n  }\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable:\n  | WeakMap<Element, PrefetchableInstance>\n  | Map<Element, PrefetchableInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible: Set<PrefetchableInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction observeVisibility(element: Element, instance: PrefetchableInstance) {\n  const existingInstance = prefetchable.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountPrefetchableInstance(element)\n  }\n  // Only track prefetchable links that have a valid prefetch URL\n  prefetchable.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction coercePrefetchableUrl(href: string): URL | null {\n  try {\n    return createPrefetchURL(href)\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return null\n  }\n}\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL,\n  prefetchEnabled: boolean,\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n): LinkInstance {\n  if (prefetchEnabled) {\n    const prefetchURL = coercePrefetchableUrl(href)\n    if (prefetchURL !== null) {\n      const instance: PrefetchableLinkInstance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus,\n      }\n      // We only observe the link's visibility if it's prefetchable. For\n      // example, this excludes links to external URLs.\n      observeVisibility(element, instance)\n      return instance\n    }\n  }\n  // If the link is not prefetchable, we still create an instance so we can\n  // track its optimistic state (i.e. useLinkStatus).\n  const instance: NonPrefetchableLinkInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: null,\n    setOptimisticLinkStatus,\n  }\n  return instance\n}\n\nexport function mountFormInstance(\n  element: HTMLFormElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n): void {\n  const prefetchURL = coercePrefetchableUrl(href)\n  if (prefetchURL === null) {\n    // This href is not prefetchable, so we don't track it.\n    // TODO: We currently observe/unobserve a form every time its href changes.\n    // For Links, this isn't a big deal because the href doesn't usually change,\n    // but for forms it's extremely common. We should optimize this.\n    return\n  }\n  const instance: FormInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: prefetchURL.href,\n    setOptimisticLinkStatus: null,\n  }\n  observeVisibility(element, instance)\n}\n\nexport function unmountPrefetchableInstance(element: Element) {\n  const instance = prefetchable.get(element)\n  if (instance !== undefined) {\n    prefetchable.delete(element)\n    prefetchableAndVisible.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(element: Element, isVisible: boolean) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    prefetchableAndVisible.add(instance)\n  } else {\n    prefetchableAndVisible.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(\n  element: HTMLAnchorElement | SVGAElement,\n  unstable_upgradeToDynamicPrefetch: boolean\n) {\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    if (\n      process.env.__NEXT_DYNAMIC_ON_HOVER &&\n      unstable_upgradeToDynamicPrefetch\n    ) {\n      // Switch to a full, dynamic prefetch\n      instance.kind = PrefetchKind.FULL\n    }\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: PrefetchableInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with reschedulePrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  const appRouterState = getCurrentAppRouterState()\n  if (appRouterState !== null) {\n    const treeAtTimeOfPrefetch = appRouterState.tree\n    if (existingPrefetchTask === null) {\n      // Initiate a prefetch task.\n      const nextUrl = appRouterState.nextUrl\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    } else {\n      // We already have an old task object that we can reschedule. This is\n      // effectively the same as canceling the old task and creating a new one.\n      reschedulePrefetchTask(\n        existingPrefetchTask,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    }\n\n    // Keep track of the cache version at the time the prefetch was requested.\n    // This is used to check if the prefetch is stale.\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of prefetchableAndVisible) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: PrefetchableInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n"], "names": ["getCurrentAppRouterState", "createPrefetchURL", "PrefetchKind", "getCurrentCacheVersion", "createCacheKey", "PrefetchPriority", "schedulePrefetchTask", "scheduleSegmentPrefetchTask", "cancelPrefetchTask", "reschedulePrefetchTask", "startTransition", "linkForMostRecentNavigation", "PENDING_LINK_STATUS", "pending", "IDLE_LINK_STATUS", "setLinkForCurrentNavigation", "link", "setOptimisticLinkStatus", "unmountLinkForCurrentNavigation", "prefetchable", "WeakMap", "Map", "prefetchableAndVisible", "Set", "observer", "IntersectionObserver", "handleIntersect", "rootMargin", "observeVisibility", "element", "instance", "existingInstance", "get", "undefined", "unmountPrefetchableInstance", "set", "observe", "coercePrefetchableUrl", "href", "reportErrorFn", "reportError", "console", "error", "mountLinkInstance", "router", "kind", "prefetchEnabled", "prefetchURL", "isVisible", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "prefetchHref", "mountFormInstance", "delete", "unobserve", "entries", "entry", "intersectionRatio", "onLinkVisibilityChanged", "target", "process", "env", "NODE_ENV", "add", "rescheduleLinkPrefetch", "onNavigationIntent", "unstable_upgradeToDynamicPrefetch", "__NEXT_DYNAMIC_ON_HOVER", "FULL", "existingPrefetchTask", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchWithOldCacheImplementation", "priority", "Intent", "<PERSON><PERSON><PERSON>", "appRouterState", "treeAtTimeOfPrefetch", "tree", "nextUrl", "cache<PERSON>ey", "pingVisibleLinks", "currentCacheVersion", "task", "key", "window", "doPrefetch", "prefetch", "catch", "err"], "mappings": "AAEA,SAASA,wBAAwB,QAAQ,wBAAuB;AAChE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,sBAAsB,QAAQ,kBAAiB;AACxD,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAEEC,gBAAgB,EAChBC,wBAAwBC,2BAA2B,EACnDC,kBAAkB,EAClBC,sBAAsB,QACjB,kBAAiB;AACxB,SAASC,eAAe,QAAQ,QAAO;AA8CvC,yEAAyE;AACzE,4DAA4D;AAC5D,IAAIC,8BAAmD;AAEvD,2CAA2C;AAC3C,OAAO,MAAMC,sBAAsB;IAAEC,SAAS;AAAK,EAAC;AAEpD,wCAAwC;AACxC,OAAO,MAAMC,mBAAmB;IAAED,SAAS;AAAM,EAAC;AAElD,0DAA0D;AAC1D,6CAA6C;AAC7C,sCAAsC;AACtC,2CAA2C;AAC3C,OAAO,SAASE,4BAA4BC,IAAyB;IACnEN,gBAAgB;QACdC,+CAAAA,4BAA6BM,uBAAuB,CAACH;QACrDE,wBAAAA,KAAMC,uBAAuB,CAACL;QAC9BD,8BAA8BK;IAChC;AACF;AAEA,8DAA8D;AAC9D,OAAO,SAASE,gCAAgCF,IAAkB;IAChE,IAAIL,gCAAgCK,MAAM;QACxCL,8BAA8B;IAChC;AACF;AAEA,2EAA2E;AAC3E,mEAAmE;AACnE,MAAMQ,eAGJ,OAAOC,YAAY,aAAa,IAAIA,YAAY,IAAIC;AAEtD,6EAA6E;AAC7E,4EAA4E;AAC5E,0EAA0E;AAC1E,iBAAiB;AACjB,MAAMC,yBAAoD,IAAIC;AAE9D,0EAA0E;AAC1E,MAAMC,WACJ,OAAOC,yBAAyB,aAC5B,IAAIA,qBAAqBC,iBAAiB;IACxCC,YAAY;AACd,KACA;AAEN,SAASC,kBAAkBC,OAAgB,EAAEC,QAA8B;IACzE,MAAMC,mBAAmBZ,aAAaa,GAAG,CAACH;IAC1C,IAAIE,qBAAqBE,WAAW;QAClC,0EAA0E;QAC1E,2EAA2E;QAC3E,+CAA+C;QAC/CC,4BAA4BL;IAC9B;IACA,+DAA+D;IAC/DV,aAAagB,GAAG,CAACN,SAASC;IAC1B,IAAIN,aAAa,MAAM;QACrBA,SAASY,OAAO,CAACP;IACnB;AACF;AAEA,SAASQ,sBAAsBC,IAAY;IACzC,IAAI;QACF,OAAOrC,kBAAkBqC;IAC3B,EAAE,UAAM;QACN,mEAAmE;QACnE,4DAA4D;QAC5D,0EAA0E;QAC1E,wEAAwE;QACxE,gCAAgC;QAChC,MAAMC,gBACJ,OAAOC,gBAAgB,aAAaA,cAAcC,QAAQC,KAAK;QACjEH,cACE,AAAC,sBAAmBD,OAAK;QAE3B,OAAO;IACT;AACF;AAEA,OAAO,SAASK,kBACdd,OAAoB,EACpBS,IAAY,EACZM,MAAyB,EACzBC,IAA2C,EAC3CC,eAAwB,EACxB7B,uBAA+D;IAE/D,IAAI6B,iBAAiB;QACnB,MAAMC,cAAcV,sBAAsBC;QAC1C,IAAIS,gBAAgB,MAAM;YACxB,MAAMjB,WAAqC;gBACzCc;gBACAC;gBACAG,WAAW;gBACXC,qBAAqB;gBACrBC,cAAc;gBACdC,cAAc,CAAC;gBACfC,cAAcL,YAAYT,IAAI;gBAC9BrB;YACF;YACA,kEAAkE;YAClE,iDAAiD;YACjDW,kBAAkBC,SAASC;YAC3B,OAAOA;QACT;IACF;IACA,yEAAyE;IACzE,mDAAmD;IACnD,MAAMA,WAAwC;QAC5Cc;QACAC;QACAG,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;QACfC,cAAc;QACdnC;IACF;IACA,OAAOa;AACT;AAEA,OAAO,SAASuB,kBACdxB,OAAwB,EACxBS,IAAY,EACZM,MAAyB,EACzBC,IAA2C;IAE3C,MAAME,cAAcV,sBAAsBC;IAC1C,IAAIS,gBAAgB,MAAM;QACxB,uDAAuD;QACvD,2EAA2E;QAC3E,4EAA4E;QAC5E,gEAAgE;QAChE;IACF;IACA,MAAMjB,WAAyB;QAC7Bc;QACAC;QACAG,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;QACfC,cAAcL,YAAYT,IAAI;QAC9BrB,yBAAyB;IAC3B;IACAW,kBAAkBC,SAASC;AAC7B;AAEA,OAAO,SAASI,4BAA4BL,OAAgB;IAC1D,MAAMC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1Bd,aAAamC,MAAM,CAACzB;QACpBP,uBAAuBgC,MAAM,CAACxB;QAC9B,MAAMoB,eAAepB,SAASoB,YAAY;QAC1C,IAAIA,iBAAiB,MAAM;YACzB1C,mBAAmB0C;QACrB;IACF;IACA,IAAI1B,aAAa,MAAM;QACrBA,SAAS+B,SAAS,CAAC1B;IACrB;AACF;AAEA,SAASH,gBAAgB8B,OAAyC;IAChE,KAAK,MAAMC,SAASD,QAAS;QAC3B,kEAAkE;QAClE,yEAAyE;QACzE,sCAAsC;QACtC,MAAMR,YAAYS,MAAMC,iBAAiB,GAAG;QAC5CC,wBAAwBF,MAAMG,MAAM,EAAuBZ;IAC7D;AACF;AAEA,OAAO,SAASW,wBAAwB9B,OAAgB,EAAEmB,SAAkB;IAC1E,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,qEAAqE;QACrE,0DAA0D;QAC1D,sCAAsC;QACtC;IACF;IAEA,MAAMjC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1B;IACF;IAEAH,SAASkB,SAAS,GAAGA;IACrB,IAAIA,WAAW;QACb1B,uBAAuB0C,GAAG,CAAClC;IAC7B,OAAO;QACLR,uBAAuBgC,MAAM,CAACxB;IAChC;IACAmC,uBAAuBnC;AACzB;AAEA,OAAO,SAASoC,mBACdrC,OAAwC,EACxCsC,iCAA0C;IAE1C,MAAMrC,WAAWX,aAAaa,GAAG,CAACH;IAClC,IAAIC,aAAaG,WAAW;QAC1B;IACF;IACA,yCAAyC;IACzC,IAAIH,aAAaG,WAAW;QAC1BH,SAASmB,mBAAmB,GAAG;QAC/B,IACEY,QAAQC,GAAG,CAACM,uBAAuB,IACnCD,mCACA;YACA,qCAAqC;YACrCrC,SAASe,IAAI,GAAG3C,aAAamE,IAAI;QACnC;QACAJ,uBAAuBnC;IACzB;AACF;AAEA,SAASmC,uBAAuBnC,QAA8B;IAC5D,MAAMwC,uBAAuBxC,SAASoB,YAAY;IAElD,IAAI,CAACpB,SAASkB,SAAS,EAAE;QACvB,0EAA0E;QAC1E,eAAe;QACf,IAAIsB,yBAAyB,MAAM;YACjC9D,mBAAmB8D;QACrB;QACA,wEAAwE;QACxE,4EAA4E;QAC5E,oEAAoE;QACpE,oDAAoD;QACpD;IACF;IAEA,IAAI,CAACT,QAAQC,GAAG,CAACS,2BAA2B,EAAE;QAC5C,2EAA2E;QAC3E,qCAAqC;QACrCC,mCAAmC1C;QACnC;IACF;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,6EAA6E;IAC7E,EAAE;IACF,6EAA6E;IAC7E,gEAAgE;IAChE,MAAM2C,WAAW3C,SAASmB,mBAAmB,GACzC5C,iBAAiBqE,MAAM,GACvBrE,iBAAiBsE,OAAO;IAC5B,MAAMC,iBAAiB5E;IACvB,IAAI4E,mBAAmB,MAAM;QAC3B,MAAMC,uBAAuBD,eAAeE,IAAI;QAChD,IAAIR,yBAAyB,MAAM;YACjC,4BAA4B;YAC5B,MAAMS,UAAUH,eAAeG,OAAO;YACtC,MAAMC,WAAW5E,eAAe0B,SAASsB,YAAY,EAAE2B;YACvDjD,SAASoB,YAAY,GAAG3C,4BACtByE,UACAH,sBACA/C,SAASe,IAAI,KAAK3C,aAAamE,IAAI,EACnCI;QAEJ,OAAO;YACL,qEAAqE;YACrE,yEAAyE;YACzEhE,uBACE6D,sBACAO,sBACA/C,SAASe,IAAI,KAAK3C,aAAamE,IAAI,EACnCI;QAEJ;QAEA,0EAA0E;QAC1E,kDAAkD;QAClD3C,SAASqB,YAAY,GAAGhD;IAC1B;AACF;AAEA,OAAO,SAAS8E,iBACdF,OAAsB,EACtBD,IAAuB;IAEvB,4EAA4E;IAC5E,6EAA6E;IAC7E,uDAAuD;IACvD,EAAE;IACF,yEAAyE;IACzE,qEAAqE;IACrE,sBAAsB;IACtB,MAAMI,sBAAsB/E;IAC5B,KAAK,MAAM2B,YAAYR,uBAAwB;QAC7C,MAAM6D,OAAOrD,SAASoB,YAAY;QAClC,IACEiC,SAAS,QACTrD,SAASqB,YAAY,KAAK+B,uBAC1BC,KAAKC,GAAG,CAACL,OAAO,KAAKA,WACrBI,KAAKN,oBAAoB,KAAKC,MAC9B;YAGA;QACF;QACA,sEAAsE;QACtE,WAAW;QACX,IAAIK,SAAS,MAAM;YACjB3E,mBAAmB2E;QACrB;QACA,MAAMH,WAAW5E,eAAe0B,SAASsB,YAAY,EAAE2B;QACvD,MAAMN,WAAW3C,SAASmB,mBAAmB,GACzC5C,iBAAiBqE,MAAM,GACvBrE,iBAAiBsE,OAAO;QAC5B7C,SAASoB,YAAY,GAAG3C,4BACtByE,UACAF,MACAhD,SAASe,IAAI,KAAK3C,aAAamE,IAAI,EACnCI;QAEF3C,SAASqB,YAAY,GAAGhD;IAC1B;AACF;AAEA,SAASqE,mCAAmC1C,QAA8B;IACxE,+DAA+D;IAC/D,IAAI,OAAOuD,WAAW,aAAa;QACjC;IACF;IAEA,MAAMC,aAAa;QACjB,sDAAsD;QACtD,wFAAwF;QACxF,OAAOxD,SAASc,MAAM,CAAC2C,QAAQ,CAACzD,SAASsB,YAAY,EAAE;YACrDP,MAAMf,SAASe,IAAI;QACrB;IACF;IAEA,kDAAkD;IAClD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDyC,aAAaE,KAAK,CAAC,CAACC;QAClB,IAAI5B,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAM0B;QACR;IACF;AACF"}